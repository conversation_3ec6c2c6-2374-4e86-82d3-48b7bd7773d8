from django.test import TestCase, override_settings
from django.core.cache import cache
from django.urls import reverse
from rest_framework import status
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

from base.factory import UserFactory
from base.services.interviews import InterviewService
from businesses.factory import CompanyFactory
from businesses.models import Company
from candidates.factory import ApplicationFactory, CandidateFactory, InterviewFactory
from jobs.factory import JobFactory
from uuid import uuid4


interview_service = InterviewService()


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class BusinessEndpointTests(TestCase):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        self.client = APIClient()

        # Create test user
        User = get_user_model()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        self.user_1 = User.objects.create_user(
            username='testuser_1',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        self.candidate = CandidateFactory(user=self.user_1, created_by=self.user_1)
        
        # Test data for creating a business
        self.valid_business_data = {
            "name": "Software Development",
            "website_url": "https://software.dev",
            "description": "Software solutions provider focusing on solar and wind power technologies.",
            "established_at": "2010-12-01",
            "number_of_employees": 195,
            "business_address": "876 Solar Road, Denver, CO 80202",
            "industry": "Development"
        }
        
        # Test data for updating a business
        self.update_business_data = {
            "name": "Clean Energy",
            "email": "<EMAIL>",
            "phone_number": "+250780000000",
            "industry": "Renewable Energy",
            "established_at": "2010-11-05",
            "business_address": "Kagarama",
            "languages": [
                {"name": "English"},
                {"name": "Kinyarwanda"}
            ],
            "business_socials": [
                {"name": "LinkedIn", "handle": "LinkedIn", "link": "https://linkedin.com/in/testuser"},
                {"name": "GitHub", "handle": "GitHub", "link": "https://github.com/testuser"}
            ],
            "services": [
                {"name": "Software Development", "category_name": "IT"},
                {"name": "Digital Marketing", "category_name": "IT"}
            ]
        }

    def tearDown(self):
        cache.clear()  # Clear cache after each test

    def _get_token(self, user):
        """Helper method to get JWT token"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)

    def test_create_business_success(self):
        """Test successful business creation"""
        url = reverse('new_business')
        token = self._get_token(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.post(url, self.valid_business_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('id', response.data)
        self.assertEqual(response.data['name'], self.valid_business_data['name'])
        self.assertIsNotNone(response.data['slug'])
        
        return response.data

    def test_create_business_unauthenticated(self):
        """Test business creation without authentication"""
        url = reverse('new_business')
        response = self.client.post(url, self.valid_business_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_business_invalid_data(self):
        """Test business creation with invalid data"""
        url = reverse('new_business')
        token = self._get_token(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        invalid_data = self.valid_business_data.copy()
        invalid_data.pop('name')  # Remove required field
        
        response = self.client.post(url, invalid_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_business_success(self):
        """Test successful business update"""
        # First create a business
        created_business = self.test_create_business_success()
        
        url = reverse('update_business', kwargs={'business_slug': created_business['slug']})
        token = self._get_token(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.patch(url, self.update_business_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], self.update_business_data['name'])
        self.assertEqual(response.data['email'], self.update_business_data['email'])
        self.assertEqual(len(response.data['languages']), 2)
        self.assertEqual(len(response.data['business_socials']), 2)
        self.assertEqual(len(response.data['services']), 2)

    def test_update_business_not_found(self):
        """Test updating non-existent business"""
        url = reverse('update_business', kwargs={'business_slug': 'non-existent-slug'})
        token = self._get_token(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.patch(url, self.update_business_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_get_business_details_success(self):
        """Test successful retrieval of business details"""
        # First create a business
        created_business = self.test_create_business_success()
        
        url = reverse('details_business', kwargs={'business_slug': created_business['slug']})
        token = self._get_token(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], created_business['id'])
        self.assertEqual(response.data['name'], created_business['name'])

    def test_get_business_details_not_found(self):
        """Test retrieving non-existent business details"""
        url = reverse('details_business', kwargs={'business_slug': 'non-existent-slug'})
        token = self._get_token(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_get_business_details_unauthenticated(self):
        """Test retrieving business details without authentication"""
        url = reverse('details_business', kwargs={'business_slug': 'some-slug'})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_get_all_business_applications(self):
        """Test successful retrieval of all business applications"""
        created_business = CompanyFactory(created_by=self.user_1)
        url = reverse('get_all_business_applications', kwargs={'business_slug': created_business.slug})
        token = self._get_token(self.user_1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_get_all_business_applications_unauthorized(self):
        created_business = CompanyFactory(created_by=self.user_1)
        url = reverse('get_all_business_applications', kwargs={'business_slug': created_business.slug})
        token = self._get_token(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_get_all_business_applications_unauthenticated(self):
        url = reverse('get_all_business_applications', kwargs={'business_slug': 'non-existent-slug'})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 403)
    
    def test_get_all_businesses(self):
        url = reverse('get_all_businesses')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_complete_profile(self):
        url = reverse("complete_profile")
        ids = [self.candidate.id]
        token = self._get_token(self.user_1)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {token}")
        response = self.client.post(url, {"candidate_ids": ids}, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["message"], "Profile completion email sent successfully")
    
    def test_complete_profile_nonexistent(self):
        url = reverse("complete_profile")
        token = self._get_token(self.user_1)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {token}")
        response = self.client.post(url, {"candidate_ids": [0,1]}, format="json")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_complete_profile_unauthenticate(self):
        url = reverse("complete_profile")
        ids = [self.candidate.id]
        response = self.client.post(url, {"candidate_ids": ids}, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_get_business_candidates(self):
        business = CompanyFactory()
        job = JobFactory(company_name=business)
        for i in range(5):
            candidate = CandidateFactory()
            application = ApplicationFactory(applicant=candidate, job_applied=job)
        url = reverse('get_business_candidates', kwargs={'business_slug': business.slug})
        token = self._get_token(self.user_1)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {token}")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_get_business_candidates_unauthenticated(self):
        business = CompanyFactory()
        job = JobFactory(company_name=business)
        for i in range(5):
            candidate = CandidateFactory()
            application = ApplicationFactory(applicant=candidate, job_applied=job)
        url = reverse('get_business_candidates', kwargs={'business_slug': business.slug})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_get_business_candidates_nonexistent(self):
        business = CompanyFactory()
        job = JobFactory(company_name=business)
        for i in range(5):
            candidate = CandidateFactory()
            application = ApplicationFactory(applicant=candidate, job_applied=job)
        url = reverse('get_business_candidates', kwargs={'business_slug': 'non-existent-slug'})
        token = self._get_token(self.user_1)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {token}")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    

class BusinessEndpointStatusTests(TestCase):
    def setUp(self):
        self.client = APIClient()

        # Create test user
        User = get_user_model()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )

        # Authenticate the user
        self.token = self._get_token()
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')

        # Create a complete business
        self.complete_business = Company.objects.create(
            name="Software Development",
            website_url="https://software.dev",
            description="Software solutions provider focusing on solar and wind power technologies.",
            established_at="2010-12-01",
            number_of_employees=195,
            business_address="876 Solar Road, Denver, CO 80202",
            industry="Development",
            email="<EMAIL>",
            phone_number="+**********",
            logo="logo.png",
            created_by=self.user
        )
        self.complete_business.languages.create(name="English")
        self.complete_business.business_socials.create(
            name="LinkedIn", handle="LinkedIn", link="https://linkedin.com/in/business"
        )
        self.complete_business.services.create(
            name="Software Development"
        )

        # Create an incomplete business
        self.incomplete_business = Company.objects.create(
            name="Incomplete Business",
            website_url="",
            description="",
            established_at=None,
            number_of_employees=None,
            business_address="",
            industry="",
            created_by=self.user
        )

    def _get_token(self):
        """Helper method to get JWT token"""
        refresh = RefreshToken.for_user(self.user)
        return str(refresh.access_token)

    def test_get_business_status_complete(self):
        """Test retrieving the status of a complete business profile"""
        url = reverse('business_status', kwargs={'business_slug': self.complete_business.slug})
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['complete'], True)
        self.assertEqual(response.data['current_step'], 2)

    def test_get_business_status_incomplete(self):
        """Test retrieving the status of an incomplete business profile"""
        url = reverse('business_status', kwargs={'business_slug': self.incomplete_business.slug})
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['complete'], False)
        self.assertEqual(response.data['current_step'], 1)

    def test_get_business_status_not_found(self):
        """Test retrieving the status of a non-existent business profile"""
        url = reverse('business_status', kwargs={'business_slug': 'non-existent-slug'})
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_get_business_status_unauthenticated(self):
        """Test retrieving business status without authentication"""
        url = reverse('business_status', kwargs={'business_slug': self.complete_business.slug})
        
        self.client.credentials()  # Remove authentication
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class DeleteBusinessTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        
        self.user = UserFactory()
        self.token = self._get_token(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {self.token}")
        
        self.business = CompanyFactory(created_by=self.user)
        
        self.other_user = UserFactory()
        self.other_business = CompanyFactory(created_by=self.other_user)
        
        self.url = reverse("delete_business", kwargs={"business_slug": self.business.slug})
    
    def _get_token(self, user):
        """Helper method to get JWT token"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    
    def test_delete_business_success(self):
        """Test successfully deleting a business."""
        response = self.client.delete(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_delete_business_with_active_jobs(self):
        """Test deleting a business that has active jobs."""
        JobFactory(company_name=self.business, status="active")
        
        response = self.client.delete(self.url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data["success"], False)
    
    def test_delete_business_with_scheduled_interviews(self):
        """Test deleting a business with scheduled interviews."""
        InterviewFactory(business=self.business, from_time="10:00", to_time="11:00")
        
        response = self.client.delete(self.url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data["success"], False)
    
    def test_delete_other_users_business(self):
        """Test attempting to delete another user's business."""
        url = reverse("delete_business", kwargs={"business_slug": self.other_business.slug})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data["success"], False)
    
    def test_delete_nonexistent_business(self):
        """Test attempting to delete a business that does not exist."""
        url = reverse("delete_business", kwargs={"business_slug": "nonexistent-slug"})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data["success"], False)
    
    def test_delete_business_unauthenticated(self):
        """Test deleting a business without authentication."""
        self.client.credentials()
        response = self.client.delete(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

class BusinessGetCandidateTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        
        # Create business user and associated company
        self.business_user = UserFactory()
        self.company = CompanyFactory(created_by=self.business_user)
        
        # candidate user and candidate profile with a valid UUID
        self.candidate_user = UserFactory()
        self.candidate = CandidateFactory(id=uuid4())
        
        # regular user without business
        self.regular_user = UserFactory()
        
        # Test URLs
        self.valid_url = reverse('get_business_candidate', kwargs={'business_slug': self.company.slug, 'candidate_id': self.candidate.id})
        self.invalid_url = reverse('get_business_candidate', kwargs={'business_slug': self.company.slug, 'candidate_id': uuid4()})

    def _get_token(self, user):
        """Generate JWT token for testing"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    
    def test_business_account_can_access(self):
        """Authenticated business user can access candidate details with candidate_id"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self._get_token(self.business_user)}')
        response = self.client.get(self.valid_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], str(self.candidate.id))  

    def test_non_business_account_forbidden(self):
        """Authenticated but non-business user gets 403"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self._get_token(self.regular_user)}')
        response = self.client.get(self.valid_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data['message'], "Permission denied. You are not the owner of this resource.")

    def test_unauthenticated_access_denied(self):
        """Unauthenticated requests get error"""
        response = self.client.get(self.valid_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn('detail', response.data) 
        self.assertEqual(response.data['detail'], "Authentication credentials were not provided.")

    def test_candidate_account_forbidden(self):
        """Candidate users can't access other candidates data (business accounts only)"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self._get_token(self.candidate_user)}')
        response = self.client.get(self.valid_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data['message'], "Permission denied. You are not the owner of this resource.")

    def test_invalid_candidate_id(self):
        """Invalid candidate_id returns 404"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self._get_token(self.business_user)}')
        response = self.client.get(self.invalid_url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['error'], "Candidate does not exist")

    def test_invalid_business_slug(self):
        """Invalid business slug returns 404"""
        invalid_url = reverse('get_business_candidate', kwargs={'business_slug': 'invalid-slug', 'candidate_id': self.candidate.id})
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self._get_token(self.business_user)}')
        response = self.client.get(invalid_url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data['message'], "Company not found.")

    def test_business_accessing_different_business(self):
        """Business user accessing a different business returns 403"""
        other_company = CompanyFactory()
        other_url = reverse('get_business_candidate', kwargs={'business_slug': other_company.slug, 'candidate_id': self.candidate.id})
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self._get_token(self.business_user)}')
        response = self.client.get(other_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data['message'], "Permission denied. You are not the owner of this resource.")