from pickle import TRUE
from django.db import models
from django.contrib.auth.models import User
from base.models import Address, BaseModel, City, SocialMediaPlatforms, UserLanguage
from imagekit.models import ImageSpecField
from imagekit.processors import ResizeToFill
from base.models import Skill, Category as SkillsCategory
from businesses.models import Company
import uuid
from jobs.models import Job


def profile_image_path(instance, filename):
    username = instance.user.username
    return f"uploads/candidates/{username}/profile_images/{filename}"


def cv_file_path(instance, filename):
    username = instance.user.username
    return f"uploads/candidates/{username}/cv/{filename}"


class WorkExperience(BaseModel):
    employment_title = models.CharField(max_length=255, null=True, blank=True)
    company_name = models.CharField(max_length=255, null=True, blank=True)
    started_from = models.DateTimeField(null=True)
    ended_at = models.DateTimeField(null=True)


class AcademicEducation(BaseModel):
    school_name = models.CharField(max_length=500, null=True)
    degree_attained = models.CharField(max_length=255, null=True, blank=True)
    started_year = models.IntegerField()
    ended_year = models.IntegerField()


class Candidate(BaseModel):
    GENDER_CHOICES = [
        ("Male", "Male"),
        ("Female", "Female"),
        ("Other", "Other"),
    ]
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    profile_picture = models.ImageField(
        upload_to=profile_image_path, blank=True, null=True
    )

    resume = models.FileField(upload_to=cv_file_path, blank=True)
    skills = models.ManyToManyField(Skill, blank=True)
    work_experience = models.ManyToManyField(
        WorkExperience,
        blank=True,
        null=True,
    )
    academic_education = models.ManyToManyField(
        AcademicEducation,
        blank=True,
        null=True,
    )
    is_employed = models.BooleanField(default=False)

    location = models.ForeignKey(
        Address,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="user_address",
    )
    linkedin_url = models.URLField(blank=True, null=True)
    portfolio_url = models.URLField(blank=True, null=True)
    gender = models.CharField(
        choices=GENDER_CHOICES, default="Female", max_length=500, null=True, blank=True
    )
    date_of_birth = models.DateTimeField(auto_now_add=False, null=True, blank=True)
    phone_number = models.CharField(max_length=255, null=True, blank=True)
    languages = models.ManyToManyField(UserLanguage)
    bio = models.CharField(max_length=500, null=True, blank=True)
    social_platforms = models.ManyToManyField(SocialMediaPlatforms)
    started_working = models.DateTimeField(auto_now_add=False, null=True)
    is_active = models.BooleanField(default=False)
    is_verified = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.user.first_name} {self.user.last_name}"

    objects = models.Manager()


class Interview(BaseModel):
    LOCATION_CHOICES = [
        ("Online", "Online"),
        ("In person", "In person"),
    ]

    STATUS_CHOICES = [
        ("Upcoming", "Upcoming"),
        ("Due", "Due"),
        ("Closed", "Closed"),
    ]
    user = models.ForeignKey(User, on_delete=models.PROTECT)
    business = models.ForeignKey(Company, on_delete=models.SET_NULL, null=True)
    job_applied = models.ForeignKey(Job, on_delete=models.SET_NULL, null=True)
    interview_date = models.DateTimeField(auto_now_add=False)
    from_time = models.TimeField(auto_now_add=False, null=True)
    to_time = models.TimeField(auto_now_add=False, null=True)
    location = models.CharField(
        max_length=255, choices=LOCATION_CHOICES, default="On site"
    )

    status = models.CharField(
        max_length=255, choices=STATUS_CHOICES, default="Upcoming"
    )
    message = models.TextField(max_length=500, null=True)

    def save(self, *args, **kwargs):
        if not self.name:
            # Generate a meaningful name for the interview
            user_name = self.user.get_full_name() or self.user.username
            business_name = self.business.name if self.business else "Unknown Business"
            job_name = self.job_applied.name if self.job_applied else "Unknown Job"
            self.name = f"Interview - {user_name} - {business_name} - {job_name}"
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.user.first_name}"


class Application(BaseModel):
    APPLICATION_CHOICES = [
        ("Pending", "Pending"),
        ("Shortlisted", "Shortlisted"),
        ("Interview", "Interview"),
        ("Hired", "Hired"),
        ("Rejected", "Rejected"),
        ("Confirmed", "Confirmed"),
    ]
    applicant = models.ForeignKey(Candidate, null=True, on_delete=models.SET_NULL)
    status = models.CharField(
        choices=APPLICATION_CHOICES, default="Pending", max_length=255
    )
    job_applied = models.ForeignKey(Job, on_delete=models.SET_NULL, null=True)
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="updated_by",
    )

    def save(self, *args, **kwargs):
        if not self.name:
            # Generate a meaningful name for the application
            applicant_name = "Unknown Applicant"
            if self.applicant and self.applicant.user:
                applicant_name = self.applicant.user.get_full_name() or self.applicant.user.username

            job_name = "Unknown Job"
            if self.job_applied:
                job_name = self.job_applied.name

            self.name = f"Application - {applicant_name} - {job_name}"
        super().save(*args, **kwargs)


class Chat(models.Model):
    MESSAGE_STATUS = [("Read", "read"), ("Unread", "unread")]
    message_id = models.UUIDField(default=uuid.uuid4, unique=True, primary_key=True)
    sender = models.OneToOneField(User, on_delete=models.CASCADE)
    receiver = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="receiver"
    )
    message = models.CharField(max_length=250)
    status = models.CharField(choices=MESSAGE_STATUS, default="Unread", max_length=255)
    date_created = models.DateTimeField(auto_now_add=True, null=True)
